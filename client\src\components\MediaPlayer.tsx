import ReactPlayer from "react-player";
import {
    MediaController,
    MediaControlBar,
    MediaTimeRange,
    MediaTimeDisplay,
    MediaVolumeRange,
    MediaPlaybackRateButton,
    MediaPlayButton,
    MediaSeekBackwardButton,
    MediaSeekForwardButton,
    MediaMuteButton,
    MediaFullscreenButton,
} from "media-chrome/react";
import { useEffect, useRef, useState, useCallback } from "react";
import type { FC } from "react";

interface MediaPlayerProps {
  user?: { role: string; name: string };
  url: string;
  onEnded?: () => void;
  lessonId?: string;
  autoAdvance?: boolean; // Enable auto-advance to next lesson
}

interface VideoProgress {
  currentTime: number;
  totalWatchedDuration: number;
  lastUpdated: number;
  completed: boolean;
}

const MediaPlayer: FC<MediaPlayerProps> = ({
  user,
  url,
  onEnded,
  lessonId,
  autoAdvance = true
}) => {
  const playerRef = useRef<ReactPlayer>(null);
  const progressIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const lastProgressUpdateRef = useRef<number>(0);

  const [isReady, setIsReady] = useState(false);
  const [hasResumed, setHasResumed] = useState(false);
  const [duration, setDuration] = useState(0);

  // Generate unique storage key for this video
  const getStorageKey = useCallback((videoUrl: string, lesson?: string) => {
    const identifier = lesson || videoUrl;
    return `video_progress_${btoa(identifier).replace(/[^a-zA-Z0-9]/g, '')}`;
  }, []);

  // Load progress from localStorage
  const loadProgress = useCallback((videoUrl: string, lesson?: string): VideoProgress | null => {
    try {
      const key = getStorageKey(videoUrl, lesson);
      const stored = localStorage.getItem(key);
      if (stored) {
        const progress: VideoProgress = JSON.parse(stored);
        console.log(`[DEBUG] MediaPlayer: Loaded progress for ${lesson || videoUrl}:`, progress);
        return progress;
      }
    } catch (error) {
      console.error("[DEBUG] MediaPlayer: Failed to load progress:", error);
    }
    return null;
  }, [getStorageKey]);

  // Save progress to localStorage
  const saveProgress = useCallback((
    videoUrl: string,
    currentTime: number,
    totalWatched: number,
    completed: boolean = false,
    lesson?: string
  ) => {
    try {
      const key = getStorageKey(videoUrl, lesson);
      const progress: VideoProgress = {
        currentTime,
        totalWatchedDuration: totalWatched,
        lastUpdated: Date.now(),
        completed
      };
      localStorage.setItem(key, JSON.stringify(progress));
      console.log(`[DEBUG] MediaPlayer: Saved progress for ${lesson || videoUrl}:`, progress);
    } catch (error) {
      console.error("[DEBUG] MediaPlayer: Failed to save progress:", error);
    }
  }, [getStorageKey]);

  // Calculate total watched duration
  const calculateWatchedDuration = useCallback((
    currentTime: number,
    previousProgress?: VideoProgress
  ): number => {
    if (!previousProgress) return currentTime;

    const timeSinceLastUpdate = currentTime - lastProgressUpdateRef.current;
    // Only add time if it's reasonable (not seeking or pausing for too long)
    if (timeSinceLastUpdate > 0 && timeSinceLastUpdate <= 10) {
      return previousProgress.totalWatchedDuration + timeSinceLastUpdate;
    }
    return previousProgress.totalWatchedDuration;
  }, []);

  // Handle progress updates every 5 seconds
  const handleProgress = useCallback(() => {
    if (!playerRef.current || !isReady) return;

    const currentTime = playerRef.current.getCurrentTime();
    const videoDuration = playerRef.current.getDuration();

    if (currentTime && videoDuration) {
      const existingProgress = loadProgress(url, lessonId);
      const totalWatched = calculateWatchedDuration(currentTime, existingProgress);

      saveProgress(url, currentTime, totalWatched, false, lessonId);
      lastProgressUpdateRef.current = currentTime;
    }
  }, [url, lessonId, isReady, loadProgress, saveProgress, calculateWatchedDuration]);

  // Start progress tracking interval
  const startProgressTracking = useCallback(() => {
    if (progressIntervalRef.current) {
      clearInterval(progressIntervalRef.current);
    }

    progressIntervalRef.current = setInterval(handleProgress, 5000); // Every 5 seconds
    console.log("[DEBUG] MediaPlayer: Started progress tracking");
  }, [handleProgress]);

  // Stop progress tracking interval
  const stopProgressTracking = useCallback(() => {
    if (progressIntervalRef.current) {
      clearInterval(progressIntervalRef.current);
      progressIntervalRef.current = null;
      console.log("[DEBUG] MediaPlayer: Stopped progress tracking");
    }
  }, []);

  // Handle when player is ready
  const handleReady = useCallback(() => {
    console.log("[DEBUG] MediaPlayer: Player ready");
    setIsReady(true);

    if (playerRef.current) {
      const videoDuration = playerRef.current.getDuration();
      setDuration(videoDuration);

      // Try to resume from last position
      const savedProgress = loadProgress(url, lessonId);
      if (savedProgress && savedProgress.currentTime > 10 && !savedProgress.completed) {
        console.log(`[DEBUG] MediaPlayer: Resuming from ${savedProgress.currentTime}s`);
        playerRef.current.seekTo(savedProgress.currentTime, 'seconds');
        setHasResumed(true);
      }
    }
  }, [url, lessonId, loadProgress]);

  // Handle when video starts playing
  const handlePlay = useCallback(() => {
    console.log("[DEBUG] MediaPlayer: Video started playing");
    startProgressTracking();
  }, [startProgressTracking]);

  // Handle when video is paused
  const handlePause = useCallback(() => {
    console.log("[DEBUG] MediaPlayer: Video paused");
    stopProgressTracking();
    // Save progress immediately when paused
    handleProgress();
  }, [stopProgressTracking, handleProgress]);

  // Handle when video ends
  const handleEnded = useCallback(() => {
    console.log("[DEBUG] MediaPlayer: Video ended");
    stopProgressTracking();

    // Mark as completed and save final progress
    if (playerRef.current) {
      const currentTime = playerRef.current.getCurrentTime();
      const videoDuration = playerRef.current.getDuration();
      const existingProgress = loadProgress(url, lessonId);
      const totalWatched = calculateWatchedDuration(currentTime, existingProgress);

      saveProgress(url, videoDuration, totalWatched, true, lessonId);
    }

    // Auto-advance to next lesson if enabled
    if (autoAdvance && onEnded) {
      console.log("[DEBUG] MediaPlayer: Auto-advancing to next lesson");
      setTimeout(() => {
        onEnded();
      }, 1000); // Small delay for better UX
    }
  }, [url, lessonId, autoAdvance, onEnded, stopProgressTracking, loadProgress, saveProgress, calculateWatchedDuration]);

  // Handle duration change
  const handleDuration = useCallback((duration: number) => {
    console.log(`[DEBUG] MediaPlayer: Duration loaded: ${duration}s`);
    setDuration(duration);
  }, []);

  // Handle seeking
  const handleSeek = useCallback((seconds: number) => {
    console.log(`[DEBUG] MediaPlayer: Seeked to ${seconds}s`);
    lastProgressUpdateRef.current = seconds;
    // Save progress after seeking
    setTimeout(handleProgress, 1000);
  }, [handleProgress]);

  // Cleanup on unmount or URL change
  useEffect(() => {
    return () => {
      stopProgressTracking();
      // Save final progress on unmount
      if (playerRef.current && isReady) {
        const currentTime = playerRef.current.getCurrentTime();
        const existingProgress = loadProgress(url, lessonId);
        const totalWatched = calculateWatchedDuration(currentTime, existingProgress);
        saveProgress(url, currentTime, totalWatched, false, lessonId);
      }
    };
  }, [url, lessonId, isReady, stopProgressTracking, loadProgress, saveProgress, calculateWatchedDuration]);

  // Reset state when URL changes
  useEffect(() => {
    setIsReady(false);
    setHasResumed(false);
    setDuration(0);
    stopProgressTracking();
  }, [url, stopProgressTracking]);

  return (
    <MediaController
      style={{ width: "100%", height: "100%", aspectRatio: "16/9", position: "relative" }}
      className="w-full h-full aspect-video bg-black rounded-lg overflow-hidden"
    >
      {user && (
        <div className="pointer-events-none absolute inset-0 z-30 flex items-center justify-center text-white/10 text-5xl font-bold select-none"
          style={{
            transform: "rotate(-20deg)",
            userSelect: "none",
            whiteSpace: "nowrap",
          }}
        >
          {user.name}
        </div>
      )}
      <ReactPlayer
        ref={playerRef}
        slot="media"
        src={url}
        playing
        controls={false}
        width="100%"
        height="100%"
        style={{ width: "100%", height: "100%", padding: 0, background: "black" }}
        config={{
          html: {
            forceVideo: true,
            attributes: {
              crossOrigin: "anonymous",
              controlsList: "nodownload",
              disablePictureInPicture: true,
              playsInline: true,
            },
          },
        }}
        onReady={handleReady}
        onPlay={handlePlay}
        onPause={handlePause}
        onEnded={handleEnded}
        onDuration={handleDuration}
        onSeek={handleSeek}
        onProgress={({ playedSeconds }) => {
          // Update last progress time for calculations
          lastProgressUpdateRef.current = playedSeconds;
        }}
      />
      <MediaControlBar className="bg-black/70 z-30" style={{ width: "100%" }}>
        <MediaPlayButton />
        <MediaSeekBackwardButton seekOffset={10} />
        <MediaSeekForwardButton seekOffset={10} />
        <MediaTimeRange />
        <MediaTimeDisplay showDuration remaining />
        <MediaMuteButton />
        <MediaVolumeRange />
        <MediaPlaybackRateButton />
        <MediaFullscreenButton />
      </MediaControlBar>
    </MediaController>
  );
};

// Utility functions for external use
export const getVideoProgress = (videoUrl: string, lessonId?: string): VideoProgress | null => {
  try {
    const identifier = lessonId || videoUrl;
    const key = `video_progress_${btoa(identifier).replace(/[^a-zA-Z0-9]/g, '')}`;
    const stored = localStorage.getItem(key);
    if (stored) {
      return JSON.parse(stored);
    }
  } catch (error) {
    console.error("Failed to get video progress:", error);
  }
  return null;
};

export const clearVideoProgress = (videoUrl: string, lessonId?: string): void => {
  try {
    const identifier = lessonId || videoUrl;
    const key = `video_progress_${btoa(identifier).replace(/[^a-zA-Z0-9]/g, '')}`;
    localStorage.removeItem(key);
    console.log(`Cleared progress for ${lessonId || videoUrl}`);
  } catch (error) {
    console.error("Failed to clear video progress:", error);
  }
};

export const getAllVideoProgress = (): Record<string, VideoProgress> => {
  const allProgress: Record<string, VideoProgress> = {};
  try {
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && key.startsWith('video_progress_')) {
        const stored = localStorage.getItem(key);
        if (stored) {
          allProgress[key] = JSON.parse(stored);
        }
      }
    }
  } catch (error) {
    console.error("Failed to get all video progress:", error);
  }
  return allProgress;
};

// Hook for using video progress in components
export const useVideoProgress = (videoUrl: string, lessonId?: string) => {
  const [progress, setProgress] = useState<VideoProgress | null>(null);

  useEffect(() => {
    const loadProgress = () => {
      const savedProgress = getVideoProgress(videoUrl, lessonId);
      setProgress(savedProgress);
    };

    loadProgress();

    // Listen for storage changes to update progress in real-time
    const handleStorageChange = (e: StorageEvent) => {
      const identifier = lessonId || videoUrl;
      const key = `video_progress_${btoa(identifier).replace(/[^a-zA-Z0-9]/g, '')}`;
      if (e.key === key) {
        loadProgress();
      }
    };

    window.addEventListener('storage', handleStorageChange);
    return () => window.removeEventListener('storage', handleStorageChange);
  }, [videoUrl, lessonId]);

  return progress;
};

export default MediaPlayer;
