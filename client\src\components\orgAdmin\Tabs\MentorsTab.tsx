import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger } from '@/components/ui/dropdown-menu'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Edit, Eye, Filter, Mail, MoreHorizontal, Search, Trash2, UserPlus } from 'lucide-react'
import React from 'react'

// Interface for mentor type
interface Mentor {
    id: string;
    name: string;
    email: string;
    specialization: string;
    studentsCount: number;
    cohortsCount: number;
    rating: number;
    status: "active" | "inactive";
    lastActive: string;
    avatar?: string;
}

// Props interface
interface MentorsTabProps {
    mentors: <PERSON><PERSON>[];
    handleViewMentor: (mentorId: string) => void;
    createMentorHandler: () => void;
    searchTerm: string;
    setSearchTerm: (term: string) => void;
}

function MentorsTab({ mentors, handleViewMentor, searchTerm, setSearchTerm }: MentorsTabProps) {


    const createMentorHandler = () => {
        console.log("Creating Mentor")
        
    }

    return (
        <div className='space-y-6'>
            <div className="flex justify-between items-center">
                <div className="flex gap-4">
                    <div className="relative">
                        <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                        <Input
                            placeholder="Search mentors..."
                            value={searchTerm}
                            onChange={(e) => setSearchTerm(e.target.value)}
                            className="pl-10 w-80"
                        />
                    </div>
                    <Select defaultValue="all">
                        <SelectTrigger className="w-40">
                            <SelectValue placeholder="Specialization" />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectItem value="all">All Specializations</SelectItem>
                            <SelectItem value="data-science">Data Science</SelectItem>
                            <SelectItem value="web-dev">Web Development</SelectItem>
                            <SelectItem value="mobile-dev">Mobile Development</SelectItem>
                            <SelectItem value="ui-ux">UI/UX Design</SelectItem>
                        </SelectContent>
                    </Select>
                    <Button variant="outline">
                        <Filter className="h-4 w-4 mr-2" />
                        Advanced Filters
                    </Button>
                </div>
                <Button className="cursor-pointer" onClick={() => createMentorHandler()}>
                    <UserPlus className="h-4 w-4" />
                    Add Mentor
                </Button>
            </div>

            <Card>
                <CardHeader>
                    <CardTitle>Mentors Overview</CardTitle>
                    <CardDescription>
                        Click on a mentor to view detailed information and manage their cohorts
                    </CardDescription>
                </CardHeader>
                <CardContent>
                    <Table>
                        <TableHeader>
                            <TableRow>
                                <TableHead>Mentor</TableHead>
                                <TableHead>Specialization</TableHead>
                                <TableHead>Students</TableHead>
                                <TableHead>Cohorts</TableHead>
                                <TableHead>Rating</TableHead>
                                <TableHead>Status</TableHead>
                                <TableHead>Last Active</TableHead>
                                <TableHead className="text-right">Actions</TableHead>
                            </TableRow>
                        </TableHeader>
                        <TableBody>
                            {mentors.map((mentor) => (
                                <TableRow key={mentor.id} className="cursor-pointer hover:bg-muted/50">
                                    <TableCell>
                                        <div className="flex items-center gap-3">
                                            <Avatar>
                                                <AvatarImage src={mentor.avatar || "/placeholder.svg"} alt={mentor.name} />
                                                <AvatarFallback>
                                                    {mentor.name
                                                        .split(" ")
                                                        .map((n) => n[0])
                                                        .join("")}
                                                </AvatarFallback>
                                            </Avatar>
                                            <div>
                                                <p className="font-medium">{mentor.name}</p>
                                                <p className="text-sm text-muted-foreground">{mentor.email}</p>
                                            </div>
                                        </div>
                                    </TableCell>
                                    <TableCell>{mentor.specialization}</TableCell>
                                    <TableCell>{mentor.studentsCount}</TableCell>
                                    <TableCell>{mentor.cohortsCount}</TableCell>
                                    <TableCell>{mentor.rating}</TableCell>
                                    <TableCell>
                                        <Badge variant={mentor.status === "active" ? "default" : "secondary"}>{mentor.status}</Badge>
                                    </TableCell>
                                    <TableCell>{mentor.lastActive}</TableCell>
                                    <TableCell className="text-right">
                                        <DropdownMenu>
                                            <DropdownMenuTrigger asChild>
                                                <Button variant="ghost" className="h-8 w-8 p-0">
                                                    <MoreHorizontal className="h-4 w-4" />
                                                </Button>
                                            </DropdownMenuTrigger>
                                            <DropdownMenuContent align="end">
                                                <DropdownMenuLabel>Actions</DropdownMenuLabel>
                                                <DropdownMenuItem onClick={() => handleViewMentor(mentor.id)}>
                                                    <Eye className="mr-2 h-4 w-4" />
                                                    View Profile
                                                </DropdownMenuItem>
                                                <DropdownMenuItem>
                                                    <Edit className="mr-2 h-4 w-4" />
                                                    Edit Profile
                                                </DropdownMenuItem>
                                                <DropdownMenuItem>
                                                    <Mail className="mr-2 h-4 w-4" />
                                                    Send Message
                                                </DropdownMenuItem>
                                                <DropdownMenuSeparator />
                                                <DropdownMenuItem className="text-red-600">
                                                    <Trash2 className="mr-2 h-4 w-4" />
                                                    Remove Mentor
                                                </DropdownMenuItem>
                                            </DropdownMenuContent>
                                        </DropdownMenu>
                                    </TableCell>
                                </TableRow>
                            ))}
                        </TableBody>
                    </Table>
                </CardContent>
            </Card>
        </div>
    )
}

export default MentorsTab