{"openapi": "3.0.0", "info": {"title": "My API", "version": "1.0.0", "description": "API documentation"}, "servers": [{"url": "http://localhost:3000"}], "tags": [{"name": "User", "description": "Operations related to user registration, login, profile, etc."}], "components": {"schemas": {"RegisterInitiateInput": {"type": "object", "required": ["name", "email", "password"], "properties": {"name": {"type": "string", "example": "<PERSON>"}, "email": {"type": "string", "example": "<EMAIL>"}, "password": {"type": "string", "example": "StrongPassword123"}, "role": {"type": "string", "enum": ["super_admin", "mentor", "student", "org_admin"], "example": "student"}, "organization": {"type": "string", "example": "60dbf4e2f65d2c3efb5b4a5e"}}}, "RegisterCompleteInput": {"type": "object", "required": ["otp", "email", "password"], "properties": {"otp": {"type": "string", "example": "123456"}, "email": {"type": "string", "example": "<EMAIL>"}, "password": {"type": "string", "example": "StrongPassword123"}}}, "LoginInput": {"type": "object", "required": ["email", "password"], "properties": {"email": {"type": "string", "example": "<EMAIL>"}, "password": {"type": "string", "example": "StrongPassword123"}}}, "UpdateProfileInput": {"type": "object", "required": ["name", "profile"], "properties": {"name": {"type": "string", "example": "<PERSON>"}, "profile": {"type": "object", "properties": {"bio": {"type": "string", "example": "MERN Stack Developer"}, "xp": {"type": "number", "example": 100}, "streak": {"type": "number", "example": 10}, "skills": {"type": "array", "items": {"type": "string"}, "example": ["typescript", "react", "node"]}}}}}}}, "paths": {"/api/user/register/initiate": {"post": {"summary": "Initiate user registration with email & password", "tags": ["User"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RegisterInitiateInput"}}}}, "responses": {"200": {"description": "OTP sent successfully"}, "400": {"description": "Invalid input or email already exists"}}}}, "/api/user/register/complete": {"post": {"summary": "Complete registration using OTP", "tags": ["User"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RegisterCompleteInput"}}}}, "responses": {"200": {"description": "User registered successfully"}, "400": {"description": "Invalid or expired OTP"}}}}, "/api/user/resend-otp": {"post": {"summary": "Resend OTP to the user email", "tags": ["User"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResendOtpInput"}}}}, "responses": {"200": {"description": "OTP resent successfully"}, "404": {"description": "User not found"}}}}, "/api/user/login": {"post": {"summary": "Log in the user", "tags": ["User"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginInput"}}}}, "responses": {"200": {"description": "Login successful"}, "401": {"description": "Invalid email or password"}}}}, "/api/user/all": {"get": {"summary": "Get all users (admin only)", "tags": ["User"], "security": [{"bearerAuth": []}], "responses": {"200": {"description": "List of users"}, "403": {"description": "Access denied"}}}}, "/api/user/profile": {"get": {"summary": "Get logged-in user profile", "tags": ["User"], "security": [{"bearerAuth": []}], "responses": {"200": {"description": "User profile fetched"}}}, "patch": {"summary": "Update user profile", "tags": ["User"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateProfileInput"}}}}, "responses": {"200": {"description": "Profile updated"}}}}, "/api/user/logout": {"post": {"summary": "Logout user and invalidate tokens", "tags": ["User"], "security": [{"bearerAuth": []}], "responses": {"200": {"description": "Logged out successfully"}}}}}}