<svg width="100" height="100" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
  <!-- Gradient Definition -->
  <defs>
    <linearGradient id="badgeGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea"/>
      <stop offset="100%" style="stop-color:#764ba2"/>
    </linearGradient>
  </defs>

  <!-- Rounded Square Badge -->
  <rect x="20" y="20" rx="15" ry="15" width="60" height="60" fill="url(#badgeGradient)" stroke="#ffffff" stroke-width="2"/>

  <!-- "EL" Text -->
  <text x="35" y="55" font-family="Arial, sans-serif" font-size="28" fill="#ffffff" font-weight="bold" text-anchor="middle">
    EL
  </text>

  <!-- Subtle Border Highlight -->
  <rect x="20" y="20" rx="15" ry="15" width="60" height="60" fill="none" stroke="#667eea" stroke-width="1" opacity="0.7"/>
</svg>