import { BookOpen, Building2, Star } from "lucide-react";
import { type SubscriptionPlans } from "./types";

export const fallbackPlans: SubscriptionPlans = {
  monthly: {
    basic: {
      name: "Basic",
      price: 0,
      description: "Ideal for individuals or trial users getting started.",
      icon: BookOpen,
      color: "bg-blue-500",
      popular: false,
      features: [
        { name: "Access to 2 cohort", included: true },
        { name: "Max 10 students per cohort", included: true },
        { name: "Basic student management", included: true },
        { name: "Limited analytics dashboard", included: true },
        { name: "Email support only", included: true },
      ],
      _id: "123",
      yearlyPrice: 0,
      originalPrice: 0,
      tax: 0,
      discount: 0,
    },
    pro: {
      name: "Pro",
      price: 1999,
      description: "Perfect for small coaching centers managing multiple cohorts.",
      icon: Star,
      color: "bg-purple-500",
      popular: true,
      features: [
        { name: "Access to 5 cohorts", included: true },
        { name: "Up to 100 students per cohort", included: true },
        { name: "Assignments and attendance management", included: true },
        { name: "Advanced analytics", included: true },
        { name: "Email + chat support", included: true },
      ],
      _id: "123",
      yearlyPrice: 0,
      originalPrice: 0,
      tax: 0,
      discount: 0,
    },
    business: {
      name: "Business",
      price: 4999,
      description: "Best for large institutions with complete control and support.",
      icon: Building2,
      color: "bg-green-500",
      popular: false,
      features: [
        { name: "Unlimited cohorts", included: true },
        { name: "Unlimited students per cohort", included: true },
        { name: "Full-featured student & teacher dashboards", included: true },
        { name: "Custom branding & domain", included: true },
        { name: "Priority 24/7 support", included: true },
      ],
      _id: "123",
      yearlyPrice: 0,
      originalPrice: 0,
      tax: 0,
      discount: 0,
    },
  },
  yearly: {
    basic: {
      name: "Basic",
      price: 0,
      description: "Ideal for individuals or trial users getting started.",
      icon: BookOpen,
      color: "bg-blue-500",
      popular: false,
      features: [
        { name: "Access to 2 cohort", included: true },
        { name: "Max 10 students per cohort", included: true },
        { name: "Basic student management", included: true },
        { name: "Limited analytics dashboard", included: true },
        { name: "Email support only", included: true },
      ],
      _id: "123",
      yearlyPrice: 0,
      originalPrice: 0,
      tax: 0,
      discount: 0,
    },
    pro: {
      name: "Pro",
      price: 19940,
      originalPrice: 23988,
      description: "Perfect for small coaching centers managing multiple cohorts.",
      icon: Star,
      color: "bg-purple-500",
      popular: true,
      features: [
        { name: "Access to 5 cohorts", included: true },
        { name: "Up to 100 students per cohort", included: true },
        { name: "Assignments and attendance management", included: true },
        { name: "Advanced analytics", included: true },
        { name: "Email + chat support", included: true },
      ],
      _id: "123",
      yearlyPrice: 0,
      tax: 0,
      discount: 0,
    },
    business: {
      name: "Business",
      price: 49850,
      originalPrice: 59988,
      description: "Best for large institutions with complete control and support.",
      icon: Building2,
      color: "bg-green-500",
      popular: false,
      features: [
        { name: "Unlimited cohorts", included: true },
        { name: "Unlimited students per cohort", included: true },
        { name: "Full-featured student & teacher dashboards", included: true },
        { name: "Custom branding & domain", included: true },
        { name: "Priority 24/7 support", included: true },
      ],
      _id: "123",
      yearlyPrice: 0,

      tax: 0,
      discount: 0,
    },
  },
};