import mongoose from "mongoose";

const userCohortProgressSchema = new mongoose.Schema(
  {
    user: { type: mongoose.Schema.Types.ObjectId, ref: "User", required: true },
    cohort: { type: mongoose.Schema.Types.ObjectId, ref: "Cohort", required: true },

    // Basic Progress
    completedLessons: [
      {
        lessonId: { type: mongoose.Schema.Types.ObjectId, ref: "Lesson" },
        completedAt: { type: Date, default: Date.now },
        timeSpent: { type: Number, default: 0 }, // seconds
      },
    ],
    totalLessons: { type: Number, default: 0 }, // Set at enrollment
    timeSpentMinutes: { type: Number, default: 0 },
    timeSpentSeconds: { type: Number, default: 0 },
    xp: { type: Number, default: 0 },

    // Progress % by content type
    byType: {
      video: { type: Number, default: 0 },
      reading: { type: Number, default: 0 },
      quiz: { type: Number, default: 0 },
      assignment: { type: Number, default: 0 },
      project: { type: Number, default: 0 },
    },

    // Streak & Engagement
    streak: { type: Number, default: 0 },
    streakDays: [{ type: Date }],

    // Achievements
    achievements: [{ type: String }],

    // Timestamps
    lastUpdated: { type: Date, default: Date.now },
    lastCompletedAt: { type: Date },
  },
  { timestamps: true }
);

export default mongoose.model("UserCohortProgress", userCohortProgressSchema);
