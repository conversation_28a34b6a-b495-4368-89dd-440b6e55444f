import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Ta<PERSON>, TabsContent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { useCreateCohortMutation, useGetmentorCohortQuery } from "@/store/features/api/cohorts/cohorts.api";
import { useGetmyorgQuery } from "@/store/features/api/mentor/mentorApi";
import { selectCurrentUser } from "@/store/features/slice/UserAuthSlice";
import type { APIErrorResponse } from "@/types";
import { Search } from "lucide-react";
import { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import { toast } from "sonner";
import Analytics from "./Analytics";
import CreateCohortDialog from "./CreateCohortDialog";
import MentorHeader from "./MentorHeader";
import PerformanceOverview from "./PerformanceOverview";
import QuickStats from "./QuickStats";
import RecentActivity from "./RecentActivity";
import StudentManagement from "./student-management";
import { StudentsTable } from "./StudentsTable";
import Tools from "./Tools";
import UpcomingEvents from "./UpcomingEvents";


const dashboardStats = {
  totalStudents: 45,
  activeStudents: 42,
  atRiskStudents: 3,
  activeCohorts: 3,
  averageProgress: 78,
  completionRate: 92,
  monthlyGrowth: 12.5,
};

const recentActivity = [
  {
    id: "1",
    type: "assignment_submitted",
    student: "John Doe",
    cohort: "Data Science Bootcamp",
    time: "2 hours ago",
    description: "Submitted Python Fundamentals Assignment",
  },
  {
    id: "2",
    type: "question_asked",
    student: "Jane Smith",
    cohort: "ML Advanced",
    time: "4 hours ago",
    description: "Asked question about neural networks",
  },
  {
    id: "3",
    type: "milestone_reached",
    student: "Bob Wilson",
    cohort: "Data Science Bootcamp",
    time: "1 day ago",
    description: "Completed Module 3: Statistics",
  },
];

export const upcomingEvents = [
  {
    id: "1",
    title: "Data Science Bootcamp - Week 6 Session",
    date: "2024-06-25",
    time: "18:00",
    type: "live_session",
    cohort: "Data Science Bootcamp",
    attendees: 25,
  },
  {
    id: "2",
    title: "ML Advanced - Project Review",
    date: "2024-06-26",
    time: "19:00",
    type: "review_session",
    cohort: "ML Advanced",
    attendees: 15,
  },
  {
    id: "3",
    title: "Office Hours",
    date: "2024-06-27",
    time: "16:00",
    type: "office_hours",
    cohort: "All Cohorts",
    attendees: 0,
  },
];

const performanceData = [
  { month: "Jan", engagement: 85, completion: 88, satisfaction: 4.7 },
  { month: "Feb", engagement: 88, completion: 90, satisfaction: 4.8 },
  { month: "Mar", engagement: 92, completion: 92, satisfaction: 4.9 },
  { month: "Apr", engagement: 89, completion: 89, satisfaction: 4.8 },
  { month: "May", engagement: 94, completion: 94, satisfaction: 4.9 },
  { month: "Jun", engagement: 91, completion: 92, satisfaction: 4.9 },
];


// Main MentorDashboard Component
export default function MentorDashboardHome() {
  const [thumbnailFile, setThumbnailFile] = useState<File | null>(null);
  const [demoVideoFile, setDemoVideoFile] = useState<File | null>(null);
  const [activeTab, setActiveTab] = useState(() => localStorage.getItem("mentor-dashboard-tab") || "overview");
  useEffect(() => {
    localStorage.setItem("mentor-dashboard-tab", activeTab);
  }, [activeTab]);
  const { data: orgData } = useGetmyorgQuery();
  const [createCohort, { isLoading: isCreatingCohort }] = useCreateCohortMutation();
  const { data } = useGetmentorCohortQuery(undefined);
  const myCohorts = data?.data?.cohorts || [];
  const user = useSelector(selectCurrentUser);
  const navigate = useNavigate();

  const handleCreateCohort = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    const formData = new FormData(event.currentTarget);

    const cohortData = {
      title: formData.get("title")?.toString() || "",
      shortDescription: formData.get("shortDescription")?.toString() || "",
      description: formData.get("description")?.toString() || "",
      mentor: formData.get("mentor")?.toString() || "",
      organization: formData.get("organization")?.toString() || "",
      startDate: formData.get("startDate")
        ? new Date(formData.get("startDate") as string).toISOString()
        : "",
      endDate: formData.get("endDate")
        ? new Date(formData.get("endDate") as string).toISOString()
        : "",
      maxCapacity: formData.get("maxCapacity")
        ? Number(formData.get("maxCapacity"))
        : 0,
      status: formData.get("status")?.toString() || "",
      category: formData.get("category")?.toString() || "",
      difficulty: formData.get("difficulty")?.toString() || "",
      schedule: formData.get("schedule")?.toString() || "",
      location: formData.get("location")?.toString() || "",
      language: formData.get("language")?.toString() || "",
      tags: formData.get("tags")
        ? formData.get("tags")!.toString().split(",").map((tag) => tag.trim())
        : [],
      prerequisites: formData.get("prerequisites")
        ? formData.get("prerequisites")!.toString().split(",").map((p) => p.trim())
        : [],
      certificateAvailable: formData.get("certificateAvailable") === "true",
      chapters: [],
      duration: formData.get("duration")
        ? Number(formData.get("duration"))
        : 0,
      price: formData.get("price")
        ? Number(formData.get("price"))
        : 0,
      originalPrice: formData.get("originalPrice")
        ? Number(formData.get("originalPrice"))
        : 0,
      discount: formData.get("discount")
        ? Number(formData.get("discount"))
        : 0,
      activateOn: formData.get("activateOn")
        ? new Date(formData.get("activateOn") as string).toISOString()
        : undefined, // ✅ NEW FIELD
    };

    const requiredFields: (keyof typeof cohortData)[] = [
      "title",
      "shortDescription",
      "description",
      "mentor",
      "organization",
      "startDate",
      "endDate",
      "maxCapacity",
      "status",
      "category",
      "difficulty",
      "language",
      "schedule",
      "activateOn",
    ];

    const missingFields = requiredFields.filter(
      (field) => cohortData[field] === undefined || cohortData[field] === ""
    );

    if (missingFields.length > 0) {
      toast.error("Please fill in all required fields", {
        description: missingFields.join(", "),
      });
      return;
    }

    if (
      isNaN(Date.parse(cohortData.startDate)) ||
      isNaN(Date.parse(cohortData.endDate))
    ) {
      toast.error("Invalid date format", {
        description: "Please ensure start and end dates are valid.",
      });
      return;
    }

    if (new Date(cohortData.startDate) >= new Date(cohortData.endDate)) {
      toast.error("Invalid date range", {
        description: "End date must be after start date.",
      });
      return;
    }

    if (cohortData.maxCapacity <= 0) {
      toast.error("Invalid capacity", {
        description: "Maximum capacity must be a positive number.",
      });
      return;
    }

    const apiFormData = new FormData();

Object.entries(cohortData).forEach(([key, value]) => {
  if (["tags", "prerequisites", "chapters"].includes(key)) {
    apiFormData.append(key, JSON.stringify(value ?? []));
  } else if (value !== undefined && value !== null) {
    apiFormData.append(key, value.toString());
  }
});


    if (thumbnailFile) {
      apiFormData.append("Thumbnail", thumbnailFile);
    }
    if (demoVideoFile) {
      apiFormData.append("demoVideo", demoVideoFile);
    }

    const toastId = toast.loading("Creating cohort...");

    try {
      await createCohort(apiFormData).unwrap();
      toast.success("Cohort created successfully!", {
        id: toastId,
      });
      window.location.reload();
      setThumbnailFile(null);
      setDemoVideoFile(null);
      // setIsDialogOpen(false); // This state is removed
    } catch (err) {
      const error = err as APIErrorResponse;
      console.error("API Error:", error);
      toast.error("Failed to create cohort", {
        id: toastId,
        description: error.data?.message || "Please try again.",
      });
    }
  };


  // The view switching logic is removed, so these functions are no longer needed.
  // const handleViewCohort = (cohortId: string) => { ... };
  // const handleViewStudent = (studentId: string) => { ... };
  // const handleBackToDashboard = () => { ... };
  // const persistCohortView = (cohortId: string, view: string) => { ... };


  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-8">
        <MentorHeader mentorData={user!} onMessagesClick={() => navigate("/dashboard/mentor/communication")} />
        <QuickStats stats={dashboardStats} />
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="cohorts">My Cohorts</TabsTrigger>
            <TabsTrigger value="students">Students</TabsTrigger>
            <TabsTrigger value="analytics">Analytics</TabsTrigger>
            <TabsTrigger value="tools">Tools</TabsTrigger>
          </TabsList>
          <TabsContent value="overview" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <RecentActivity activities={recentActivity} />
              <UpcomingEvents events={upcomingEvents} />
            </div>
            <PerformanceOverview performanceData={performanceData} />
          </TabsContent>
          <TabsContent value="cohorts" className="space-y-6">
            <div className="flex justify-between items-center">
              <h3 className="text-lg font-semibold">My Cohorts</h3>
              <CreateCohortDialog
                isOpen={false} // isDialogOpen state is removed
                onOpenChange={() => {}} // isDialogOpen state is removed
                orgData={orgData || []}
                onCreateCohort={handleCreateCohort}
                isCreatingCohort={isCreatingCohort}
                thumbnailFile={thumbnailFile}
                setThumbnailFile={setThumbnailFile}
                demoVideoFile={demoVideoFile}
                setDemoVideoFile={setDemoVideoFile}
              />
            </div>
            <StudentManagement
              cohorts={myCohorts}
              onViewCohort={cohortId => navigate(`/dashboard/mentor/cohort/${cohortId}`)}
              onViewStudent={studentId => navigate(`/dashboard/mentor/student/${studentId}`)}
            />
          </TabsContent>
          <TabsContent value="students" className="space-y-6">
            <div className="flex justify-between items-center">
              <h3 className="text-lg font-medium">All Students</h3>
              <div className="flex gap-2">
                <div className="relative">
                  <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                  <Input placeholder="Search students..." className="pl-10 w-80" />
                </div>
                <Select defaultValue="all">
                  <SelectTrigger className="w-40">
                    <SelectValue placeholder="Filter by status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Students</SelectItem>
                    <SelectItem value="active">Active</SelectItem>
                    <SelectItem value="at-risk">At Risk</SelectItem>
                    <SelectItem value="completed">Completed</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <StudentsTable onViewStudent={studentId => navigate(`/dashboard/mentor/student/${studentId}`)} />
          </TabsContent>
          <TabsContent value="analytics" className="space-y-6">
            <Analytics performanceData={performanceData} />
          </TabsContent>
          <TabsContent value="tools" className="space-y-6">
            <Tools
              onCurriculumClick={() => navigate(`/dashboard/mentor/
                /${myCohorts[0]?._id || ""}/curriculum`)}
              onCommunicationClick={() => navigate(`/dashboard/mentor/communication`)}
            />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}