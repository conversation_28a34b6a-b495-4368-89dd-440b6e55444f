{
  "compilerOptions": {
    "target": "ES2022",
    "module": "ESNext",
    "moduleResolution": "node",
    "outDir": "dist",
    "rootDir": ".",
    "strict": true,
    "esModuleInterop": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true,
    "resolveJsonModule": true,
    "baseUrl": ".",
   "paths": {
      "@/*": ["src/*"]
    }
  
  },
  "include": ["src","index.ts", "scripts"],
  "exclude": ["node_modules", "dist"],
  "tsc-alias": {
    "resolveFullPaths": true, // Ensures full paths are resolved, including extensions
    "verbose": false // Set to true if you want to see detailed output from tsc-alias
  }
}
