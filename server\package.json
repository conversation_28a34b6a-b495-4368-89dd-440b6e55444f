{"name": "server", "version": "1.0.0", "main": "index.js", "type": "module", "scripts": {"dev": "nodemon", "build": "tsc && tsc-alias", "start": "node dist/index.js", "start-cron": "ts-node src/utils/Cron/subscriptionCron.ts", "clean": "rm -rf dist", "generate:swagger": "ts-node src/scripts/generateSwagger.ts", "swagger": "tsx scripts/generateSwagger.ts"}, "keywords": [], "author": "", "license": "ISC", "description": "", "devDependencies": {"@types/bcrypt": "^5.0.2", "@types/cookie-parser": "^1.4.8", "@types/cors": "^2.8.18", "@types/express": "^5.0.2", "@types/jsonwebtoken": "^9.0.9", "@types/morgan": "^1.9.9", "@types/multer": "^1.4.13", "@types/node": "^22.15.21", "@types/nodemailer": "^6.4.17", "@types/sanitize-html": "^2.16.0", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.8", "nodemon": "^3.1.10", "ts-node": "^10.9.2", "tsc-alias": "^1.8.16", "tsx": "^4.19.4", "typescript": "^5.8.3"}, "dependencies": {"@stripe/react-stripe-js": "^3.7.0", "@stripe/stripe-js": "^7.3.1", "bcrypt": "^6.0.0", "chalk": "^5.4.1", "cloudinary": "^2.7.0", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^16.5.0", "express-async-handler": "^1.2.0", "express-rate-limit": "^7.5.0", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.15.0", "morgan": "^1.10.0", "multer": "^2.0.1", "node-cache": "^5.1.2", "nodemailer": "^7.0.3", "razorpay": "^2.9.6", "sanitize-html": "^2.17.0", "slugify": "^1.6.6", "stripe": "^18.2.1", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "winston": "^3.17.0", "zod": "^3.25.56"}}