// Theme Provider
export { ThemeProvider, useTheme } from "./theme-provider"

// Mode Toggle (Original)
export { ModeToggle } from "./mode-toggle"

// Dynamic Color Picker
export { 
  DynamicColorPicker, 
  CompactColorPicker 
} from "./dynamic-color-picker"

// Simple Color Picker (for testing)
export { 
  SimpleColorPicker 
} from "./simple-color-picker"

// Combined Theme Switchers
export { 
  CombinedThemeSwitcher, 
  CompactThemeSwitcher 
} from "./combined-theme-switcher"

// Demo Components
export { ThemeDemo } from "./theme-demo"
export { DynamicThemeDemo } from "./dynamic-theme-demo"

// Types
export type { Theme, CustomColors } from "./theme-provider" 