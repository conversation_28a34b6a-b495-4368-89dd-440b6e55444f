"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Progress } from "@/components/ui/progress"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
    Users,
    Calendar,
    TrendingUp,
    MessageSquare,
    Download,
    Plus,
    Eye,
    Edit,
    X,
    Search,
    AlertTriangle,
    CheckCircle,
    Clock,
    BarChart3,
} from "lucide-react"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuLabel,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { ChartContainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart"
import { Line, LineChart, XAxis, YAxis, CartesianGrid, ResponsiveContainer } from "recharts"

interface CohortDetailViewProps {
    cohortId: string
    onClose: () => void
    onViewStudent: (studentId: string) => void
}

// Enhanced cohort data
const cohortDetails = {
    "1": {
        id: "1",
        name: "Data Science Bootcamp - Fall 2024",
        mentor: "Dr. Sarah Johnson",
        mentorId: "1",
        startDate: "2024-09-01",
        endDate: "2024-12-15",
        studentsCount: 25,
        maxCapacity: 30,
        status: "active",
        progress: 65,
        category: "Data Science",
        difficulty: "Intermediate",
        description:
            "Comprehensive data science bootcamp covering Python, machine learning, statistics, and real-world projects.",
        schedule: "Monday, Wednesday, Friday - 6:00 PM to 9:00 PM EST",
        location: "Virtual (Zoom)",
        completionRate: 92,
        averageGrade: "B+",
        weeklyProgress: [
            { week: "Week 1", completion: 95, engagement: 88 },
            { week: "Week 2", completion: 92, engagement: 85 },
            { week: "Week 3", completion: 88, engagement: 82 },
            { week: "Week 4", completion: 90, engagement: 87 },
            { week: "Week 5", completion: 85, engagement: 83 },
            { week: "Week 6", completion: 87, engagement: 86 },
        ],
        curriculum: [
            {
                module: "Python Fundamentals",
                weeks: "1-2",
                status: "completed",
                topics: ["Variables & Data Types", "Control Structures", "Functions", "Object-Oriented Programming"],
            },
            {
                module: "Data Analysis with Pandas",
                weeks: "3-4",
                status: "completed",
                topics: ["DataFrames", "Data Cleaning", "Exploratory Data Analysis", "Data Visualization"],
            },
            {
                module: "Statistics & Probability",
                weeks: "5-6",
                status: "in-progress",
                topics: ["Descriptive Statistics", "Probability Distributions", "Hypothesis Testing", "Correlation"],
            },
            {
                module: "Machine Learning",
                weeks: "7-10",
                status: "upcoming",
                topics: ["Supervised Learning", "Unsupervised Learning", "Model Evaluation", "Feature Engineering"],
            },
        ],
        students: [
            {
                id: "s1",
                name: "John Doe",
                email: "<EMAIL>",
                avatar: "/placeholder.svg?height=40&width=40",
                progress: 78,
                lastActive: "2 hours ago",
                status: "active",
                joinDate: "2024-09-01",
                assignments: 12,
                completedAssignments: 9,
                grade: "A-",
                attendanceRate: 95,
                engagementScore: 88,
                lastSubmission: "2024-06-20",
                notes: "Excellent performance, very engaged in discussions",
            },
            {
                id: "s2",
                name: "Jane Smith",
                email: "<EMAIL>",
                avatar: "/placeholder.svg?height=40&width=40",
                progress: 85,
                lastActive: "1 day ago",
                status: "active",
                joinDate: "2024-09-01",
                assignments: 12,
                completedAssignments: 10,
                grade: "A",
                attendanceRate: 100,
                engagementScore: 92,
                lastSubmission: "2024-06-19",
                notes: "Top performer, helps other students",
            },
            {
                id: "s3",
                name: "Bob Wilson",
                email: "<EMAIL>",
                avatar: "/placeholder.svg?height=40&width=40",
                progress: 45,
                lastActive: "3 days ago",
                status: "at-risk",
                joinDate: "2024-09-01",
                assignments: 12,
                completedAssignments: 5,
                grade: "C",
                attendanceRate: 70,
                engagementScore: 65,
                lastSubmission: "2024-06-15",
                notes: "Struggling with assignments, needs additional support",
            },
            {
                id: "s4",
                name: "Alice Brown",
                email: "<EMAIL>",
                avatar: "/placeholder.svg?height=40&width=40",
                progress: 72,
                lastActive: "1 hour ago",
                status: "active",
                joinDate: "2024-09-01",
                assignments: 12,
                completedAssignments: 8,
                grade: "B+",
                attendanceRate: 90,
                engagementScore: 85,
                lastSubmission: "2024-06-20",
                notes: "Consistent progress, good participation",
            },
        ],
        assignments: [
            {
                id: "a1",
                title: "Python Basics Assessment",
                dueDate: "2024-09-15",
                status: "completed",
                submissionRate: 100,
                averageScore: 87,
            },
            {
                id: "a2",
                title: "Data Cleaning Project",
                dueDate: "2024-10-01",
                status: "completed",
                submissionRate: 96,
                averageScore: 82,
            },
            {
                id: "a3",
                title: "Statistical Analysis Report",
                dueDate: "2024-10-15",
                status: "active",
                submissionRate: 75,
                averageScore: 85,
            },
        ],
        communicationLogs: [
            {
                id: "1",
                type: "announcement",
                author: "Dr. Sarah Johnson",
                date: "2024-06-20",
                subject: "Week 6 Materials Available",
                content: "New materials for statistics module have been uploaded to the learning platform.",
            },
            {
                id: "2",
                type: "discussion",
                author: "Jane Smith",
                date: "2024-06-19",
                subject: "Question about Hypothesis Testing",
                content: "Can someone explain the difference between Type I and Type II errors?",
            },
        ],
    },
}

export default function CohortDetailView({ cohortId, onClose, onViewStudent }: CohortDetailViewProps) {
    const [activeTab, setActiveTab] = useState("overview")
    const [studentFilter, setStudentFilter] = useState("all")
    const [searchTerm, setSearchTerm] = useState("")

    const cohort = cohortDetails[cohortId as keyof typeof cohortDetails]

    if (!cohort) {
        return (
            <div className="text-center py-8">
                <p className="text-muted-foreground">Cohort not found</p>
                <Button onClick={onClose} className="mt-4">
                    Go Back
                </Button>
            </div>
        )
    }

    const filteredStudents = cohort.students.filter((student) => {
        const matchesSearch =
            student.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
            student.email.toLowerCase().includes(searchTerm.toLowerCase())
        const matchesFilter = studentFilter === "all" || student.status === studentFilter
        return matchesSearch && matchesFilter
    })

    const getStatusIcon = (status: string) => {
        switch (status) {
            case "completed":
                return <CheckCircle className="h-4 w-4 text-green-500" />
            case "in-progress":
                return <Clock className="h-4 w-4 text-yellow-500" />
            case "upcoming":
                return <Calendar className="h-4 w-4 text-gray-500" />
            default:
                return null
        }
    }

    return (
        <div className="space-y-6 p-4">
            {/* Header */}
            <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                    <Button variant="ghost" onClick={onClose}>
                        <X className="h-4 w-4 mr-2" />
                        Back to Mentor Profile
                    </Button>
                    <div className="h-6 w-px bg-border" />
                    <h1 className="text-2xl font-bold">Cohort Details</h1>
                </div>
                <div className="flex gap-2">
                    <Button variant="outline">
                        <Download className="h-4 w-4 mr-2" />
                        Export Report
                    </Button>
                    <Button variant="outline">
                        <MessageSquare className="h-4 w-4 mr-2" />
                        Send Announcement
                    </Button>
                </div>
            </div>

            {/* Cohort Overview Card */}
            <Card>
                <CardContent className="pt-6">
                    <div className="flex items-start justify-between mb-4">
                        <div>
                            <h2 className="text-2xl font-bold mb-2">{cohort.name}</h2>
                            <p className="text-muted-foreground mb-2">{cohort.description}</p>
                            <div className="flex items-center gap-4 text-sm text-muted-foreground">
                                <span>Mentor: {cohort.mentor}</span>
                                <span>•</span>
                                <span>{cohort.category}</span>
                                <span>•</span>
                                <span>{cohort.difficulty}</span>
                            </div>
                        </div>
                        <Badge variant={cohort.status === "active" ? "default" : "secondary"} className="text-sm">
                            {cohort.status}
                        </Badge>
                    </div>

                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                        <div className="text-center">
                            <div className="text-2xl font-bold text-primary">
                                {cohort.studentsCount}/{cohort.maxCapacity}
                            </div>
                            <div className="text-sm text-muted-foreground">Students Enrolled</div>
                        </div>
                        <div className="text-center">
                            <div className="text-2xl font-bold text-primary">{cohort.progress}%</div>
                            <div className="text-sm text-muted-foreground">Progress</div>
                        </div>
                        <div className="text-center">
                            <div className="text-2xl font-bold text-primary">{cohort.completionRate}%</div>
                            <div className="text-sm text-muted-foreground">Completion Rate</div>
                        </div>
                        <div className="text-center">
                            <div className="text-2xl font-bold text-primary">{cohort.averageGrade}</div>
                            <div className="text-sm text-muted-foreground">Average Grade</div>
                        </div>
                    </div>
                </CardContent>
            </Card>

            {/* Detailed Information Tabs */}
            <Tabs value={activeTab} onValueChange={setActiveTab}>
                <TabsList className="grid w-full grid-cols-5">
                    <TabsTrigger value="overview">Overview</TabsTrigger>
                    <TabsTrigger value="students">Students</TabsTrigger>
                    <TabsTrigger value="curriculum">Curriculum</TabsTrigger>
                    <TabsTrigger value="assignments">Assignments</TabsTrigger>
                    <TabsTrigger value="communication">Communication</TabsTrigger>
                </TabsList>

                {/* Overview Tab */}
                <TabsContent value="overview" className="space-y-6">
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        {/* Schedule & Details */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <Calendar className="h-5 w-5" />
                                    Schedule & Details
                                </CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div>
                                    <p className="font-medium">Schedule</p>
                                    <p className="text-sm text-muted-foreground">{cohort.schedule}</p>
                                </div>
                                <div>
                                    <p className="font-medium">Duration</p>
                                    <p className="text-sm text-muted-foreground">
                                        {new Date(cohort.startDate).toLocaleDateString()} - {new Date(cohort.endDate).toLocaleDateString()}
                                    </p>
                                </div>
                                <div>
                                    <p className="font-medium">Location</p>
                                    <p className="text-sm text-muted-foreground">{cohort.location}</p>
                                </div>
                                <div>
                                    <p className="font-medium">Capacity</p>
                                    <p className="text-sm text-muted-foreground">
                                        {cohort.studentsCount} of {cohort.maxCapacity} students enrolled
                                    </p>
                                    <Progress value={(cohort.studentsCount / cohort.maxCapacity) * 100} className="h-2 mt-1" />
                                </div>
                            </CardContent>
                        </Card>

                        {/* Progress Analytics */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <BarChart3 className="h-5 w-5" />
                                    Progress Analytics
                                </CardTitle>
                            </CardHeader>
                            <CardContent>
                                <ChartContainer
                                    config={{
                                        completion: {
                                            label: "Completion Rate",
                                            color: "hsl(var(--chart-1))",
                                        },
                                        engagement: {
                                            label: "Engagement Score",
                                            color: "hsl(var(--chart-2))",
                                        },
                                    }}
                                    className="h-[200px]"
                                >
                                    <ResponsiveContainer width="100%" height="100%">
                                        <LineChart data={cohort.weeklyProgress}>
                                            <CartesianGrid strokeDasharray="3 3" />
                                            <XAxis dataKey="week" />
                                            <YAxis />
                                            <ChartTooltip content={<ChartTooltipContent />} />
                                            <Line type="monotone" dataKey="completion" stroke="var(--color-completion)" strokeWidth={2} />
                                            <Line type="monotone" dataKey="engagement" stroke="var(--color-engagement)" strokeWidth={2} />
                                        </LineChart>
                                    </ResponsiveContainer>
                                </ChartContainer>
                            </CardContent>
                        </Card>
                    </div>

                    {/* Quick Stats */}
                    <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                        <Card>
                            <CardContent className="pt-6">
                                <div className="flex items-center justify-between">
                                    <div>
                                        <p className="text-sm font-medium text-muted-foreground">Active Students</p>
                                        <p className="text-2xl font-bold">{cohort.students.filter((s) => s.status === "active").length}</p>
                                    </div>
                                    <Users className="h-8 w-8 text-muted-foreground" />
                                </div>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardContent className="pt-6">
                                <div className="flex items-center justify-between">
                                    <div>
                                        <p className="text-sm font-medium text-muted-foreground">At-Risk Students</p>
                                        <p className="text-2xl font-bold text-orange-600">
                                            {cohort.students.filter((s) => s.status === "at-risk").length}
                                        </p>
                                    </div>
                                    <AlertTriangle className="h-8 w-8 text-orange-600" />
                                </div>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardContent className="pt-6">
                                <div className="flex items-center justify-between">
                                    <div>
                                        <p className="text-sm font-medium text-muted-foreground">Avg Attendance</p>
                                        <p className="text-2xl font-bold">
                                            {Math.round(
                                                cohort.students.reduce((acc, s) => acc + s.attendanceRate, 0) / cohort.students.length,
                                            )}
                                            %
                                        </p>
                                    </div>
                                    <CheckCircle className="h-8 w-8 text-muted-foreground" />
                                </div>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardContent className="pt-6">
                                <div className="flex items-center justify-between">
                                    <div>
                                        <p className="text-sm font-medium text-muted-foreground">Avg Engagement</p>
                                        <p className="text-2xl font-bold">
                                            {Math.round(
                                                cohort.students.reduce((acc, s) => acc + s.engagementScore, 0) / cohort.students.length,
                                            )}
                                            %
                                        </p>
                                    </div>
                                    <TrendingUp className="h-8 w-8 text-muted-foreground" />
                                </div>
                            </CardContent>
                        </Card>
                    </div>
                </TabsContent>

                {/* Students Tab */}
                <TabsContent value="students" className="space-y-6">
                    <div className="flex justify-between items-center">
                        <div className="flex gap-4">
                            <div className="relative">
                                <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                                <Input
                                    placeholder="Search students..."
                                    value={searchTerm}
                                    onChange={(e) => setSearchTerm(e.target.value)}
                                    className="pl-10 w-80"
                                />
                            </div>
                            <Select value={studentFilter} onValueChange={setStudentFilter}>
                                <SelectTrigger className="w-40">
                                    <SelectValue placeholder="Filter by status" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="all">All Students</SelectItem>
                                    <SelectItem value="active">Active</SelectItem>
                                    <SelectItem value="at-risk">At Risk</SelectItem>
                                    <SelectItem value="inactive">Inactive</SelectItem>
                                </SelectContent>
                            </Select>
                        </div>
                        <Button>
                            <Plus className="h-4 w-4 mr-2" />
                            Add Student
                        </Button>
                    </div>

                    <Card>
                        <CardHeader>
                            <CardTitle>Student List ({filteredStudents.length})</CardTitle>
                            <CardDescription>Manage and monitor student progress in this cohort</CardDescription>
                        </CardHeader>
                        <CardContent>
                            <Table>
                                <TableHeader>
                                    <TableRow>
                                        <TableHead>Student</TableHead>
                                        <TableHead>Progress</TableHead>
                                        <TableHead>Grade</TableHead>
                                        <TableHead>Attendance</TableHead>
                                        <TableHead>Status</TableHead>
                                        <TableHead>Last Active</TableHead>
                                        <TableHead className="text-right">Actions</TableHead>
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    {filteredStudents.map((student) => (
                                        <TableRow key={student.id}>
                                            <TableCell>
                                                <div className="flex items-center gap-3">
                                                    <Avatar>
                                                        <AvatarImage src={student.avatar || "/placeholder.svg"} alt={student.name} />
                                                        <AvatarFallback>
                                                            {student.name
                                                                .split(" ")
                                                                .map((n) => n[0])
                                                                .join("")}
                                                        </AvatarFallback>
                                                    </Avatar>
                                                    <div>
                                                        <p className="font-medium">{student.name}</p>
                                                        <p className="text-sm text-muted-foreground">{student.email}</p>
                                                    </div>
                                                </div>
                                            </TableCell>
                                            <TableCell>
                                                <div className="space-y-1">
                                                    <div className="flex justify-between text-sm">
                                                        <span>{student.progress}%</span>
                                                        <span className="text-muted-foreground">
                              {student.completedAssignments}/{student.assignments}
                            </span>
                                                    </div>
                                                    <Progress value={student.progress} className="h-2" />
                                                </div>
                                            </TableCell>
                                            <TableCell>
                                                <Badge variant="outline">{student.grade}</Badge>
                                            </TableCell>
                                            <TableCell>{student.attendanceRate}%</TableCell>
                                            <TableCell>
                                                <Badge
                                                    variant={
                                                        student.status === "active"
                                                            ? "default"
                                                            : student.status === "at-risk"
                                                                ? "destructive"
                                                                : "secondary"
                                                    }
                                                >
                                                    {student.status}
                                                </Badge>
                                            </TableCell>
                                            <TableCell>{student.lastActive}</TableCell>
                                            <TableCell className="text-right">
                                                <DropdownMenu>
                                                    <DropdownMenuTrigger asChild>
                                                        <Button variant="ghost" className="h-8 w-8 p-0">
                                                            <Eye className="h-4 w-4" />
                                                        </Button>
                                                    </DropdownMenuTrigger>
                                                    <DropdownMenuContent align="end">
                                                        <DropdownMenuLabel>Actions</DropdownMenuLabel>
                                                        <DropdownMenuItem onClick={() => onViewStudent(student.id)}>
                                                            <Eye className="mr-2 h-4 w-4" />
                                                            View Profile
                                                        </DropdownMenuItem>
                                                        <DropdownMenuItem>
                                                            <MessageSquare className="mr-2 h-4 w-4" />
                                                            Send Message
                                                        </DropdownMenuItem>
                                                        <DropdownMenuItem>
                                                            <Edit className="mr-2 h-4 w-4" />
                                                            Edit Details
                                                        </DropdownMenuItem>
                                                        <DropdownMenuSeparator />
                                                        <DropdownMenuItem className="text-red-600">Remove from Cohort</DropdownMenuItem>
                                                    </DropdownMenuContent>
                                                </DropdownMenu>
                                            </TableCell>
                                        </TableRow>
                                    ))}
                                </TableBody>
                            </Table>
                        </CardContent>
                    </Card>
                </TabsContent>

                {/* Curriculum Tab */}
                <TabsContent value="curriculum" className="space-y-6">
                    <Card>
                        <CardHeader>
                            <CardTitle>Curriculum Overview</CardTitle>
                            <CardDescription>Course modules and learning progression</CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-4">
                                {cohort.curriculum.map((module, index) => (
                                    <div key={index} className="border rounded-lg p-4">
                                        <div className="flex items-center justify-between mb-3">
                                            <div className="flex items-center gap-3">
                                                {getStatusIcon(module.status)}
                                                <div>
                                                    <h4 className="font-medium">{module.module}</h4>
                                                    <p className="text-sm text-muted-foreground">Weeks {module.weeks}</p>
                                                </div>
                                            </div>
                                            <Badge
                                                variant={
                                                    module.status === "completed"
                                                        ? "default"
                                                        : module.status === "in-progress"
                                                            ? "secondary"
                                                            : "outline"
                                                }
                                            >
                                                {module.status}
                                            </Badge>
                                        </div>
                                        <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                                            {module.topics.map((topic, topicIndex) => (
                                                <div key={topicIndex} className="text-sm p-2 bg-muted rounded">
                                                    {topic}
                                                </div>
                                            ))}
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </CardContent>
                    </Card>
                </TabsContent>

                {/* Assignments Tab */}
                <TabsContent value="assignments" className="space-y-6">
                    <div className="flex justify-between items-center">
                        <h3 className="text-lg font-semibold">Assignments & Assessments</h3>
                        <Button>
                            <Plus className="h-4 w-4 mr-2" />
                            Create Assignment
                        </Button>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                        {cohort.assignments.map((assignment) => (
                            <Card key={assignment.id}>
                                <CardHeader>
                                    <div className="flex justify-between items-start">
                                        <CardTitle className="text-lg">{assignment.title}</CardTitle>
                                        <Badge
                                            variant={
                                                assignment.status === "completed"
                                                    ? "default"
                                                    : assignment.status === "active"
                                                        ? "secondary"
                                                        : "outline"
                                            }
                                        >
                                            {assignment.status}
                                        </Badge>
                                    </div>
                                    <CardDescription>Due: {new Date(assignment.dueDate).toLocaleDateString()}</CardDescription>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    <div className="space-y-2">
                                        <div className="flex justify-between text-sm">
                                            <span>Submission Rate</span>
                                            <span>{assignment.submissionRate}%</span>
                                        </div>
                                        <Progress value={assignment.submissionRate} className="h-2" />
                                    </div>
                                    <div className="text-center">
                                        <div className="text-2xl font-bold">{assignment.averageScore}</div>
                                        <div className="text-sm text-muted-foreground">Average Score</div>
                                    </div>
                                    <Button variant="outline" className="w-full">
                                        <Eye className="h-4 w-4 mr-2" />
                                        View Submissions
                                    </Button>
                                </CardContent>
                            </Card>
                        ))}
                    </div>
                </TabsContent>

                {/* Communication Tab */}
                <TabsContent value="communication" className="space-y-6">
                    <div className="flex justify-between items-center">
                        <h3 className="text-lg font-semibold">Communication History</h3>
                        <Button>
                            <Plus className="h-4 w-4 mr-2" />
                            New Announcement
                        </Button>
                    </div>

                    <Card>
                        <CardHeader>
                            <CardTitle>Recent Communications</CardTitle>
                            <CardDescription>Announcements, discussions, and messages</CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-4">
                                {cohort.communicationLogs.map((log) => (
                                    <div key={log.id} className="border rounded-lg p-4">
                                        <div className="flex items-center justify-between mb-2">
                                            <div className="flex items-center gap-2">
                                                <Badge variant="outline">{log.type}</Badge>
                                                <h4 className="font-medium">{log.subject}</h4>
                                            </div>
                                            <span className="text-sm text-muted-foreground">{log.date}</span>
                                        </div>
                                        <p className="text-sm text-muted-foreground mb-2">By: {log.author}</p>
                                        <p className="text-sm">{log.content}</p>
                                    </div>
                                ))}
                            </div>
                        </CardContent>
                    </Card>
                </TabsContent>
            </Tabs>
        </div>
    )
}
