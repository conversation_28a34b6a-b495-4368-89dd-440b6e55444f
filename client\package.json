{"name": "tailwind-shadcn-slate", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@codemirror/lang-cpp": "^6.0.3", "@codemirror/lang-css": "^6.3.1", "@codemirror/lang-go": "^6.0.1", "@codemirror/lang-html": "^6.4.9", "@codemirror/lang-java": "^6.0.2", "@codemirror/lang-javascript": "^6.2.4", "@codemirror/lang-markdown": "^6.3.3", "@codemirror/lang-php": "^6.0.2", "@codemirror/lang-python": "^6.2.1", "@codemirror/lang-rust": "^6.0.2", "@excalidraw/excalidraw": "^0.18.0", "@hookform/resolvers": "^5.1.1", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.7", "@reduxjs/toolkit": "^2.8.2", "@stripe/react-stripe-js": "^3.7.0", "@stripe/stripe-js": "^7.3.1", "@tailwindcss/vite": "^4.1.7", "@uiw/react-codemirror": "^4.24.1", "chart.js": "^4.5.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "codemirror": "^6.0.2", "crypto-js": "^4.2.0", "culori": "^4.0.2", "date-fn": "^0.0.2", "date-fns": "^4.1.0", "framer-motion": "^12.23.3", "input-otp": "^1.4.2", "jwt-decode": "^4.0.0", "lucide-react": "^0.511.0", "next-themes": "^0.4.6", "react": "^19.1.0", "react-confetti": "^6.4.0", "react-dom": "^19.1.0", "react-hook-form": "^7.57.0", "react-markdown": "^10.1.0", "react-player": "^3.2.0", "react-redux": "^9.2.0", "react-router-dom": "^7.6.2", "react-syntax-highlighter": "^15.6.1", "recharts": "^2.15.3", "rehype-raw": "^7.0.0", "remark": "^15.0.1", "remark-emoji": "^5.0.1", "remark-gfm": "^4.0.1", "sonner": "^2.0.5", "stripe": "^18.2.1", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.7", "zod": "^3.25.56"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/crypto-js": "^4.2.2", "@types/culori": "^4.0.0", "@types/lodash": "^4.17.17", "@types/node": "^22.15.21", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@types/react-syntax-highlighter": "^15.5.13", "@vitejs/plugin-react": "^4.4.1", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "rollup-plugin-visualizer": "^6.0.3", "tw-animate-css": "^1.3.4", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5", "vite-plugin-compression": "^0.5.1"}}